// 测试节点查找功能
const fs = require('fs');

// 模拟 findNodeById 函数
function findNodeById(node, targetId) {
  if (!node || typeof node !== 'object') {
    return null;
  }

  // 检查当前节点是否匹配
  if (node.id === targetId) {
    return node;
  }

  // 递归搜索子节点
  if (node.children && Array.isArray(node.children)) {
    for (const child of node.children) {
      const found = findNodeById(child, targetId);
      if (found) {
        return found;
      }
    }
  }

  return null;
}

// 模拟 cleanNodeData 函数
function cleanNodeData(node) {
  if (!node || typeof node !== 'object') {
    return node;
  }

  // 创建节点的浅拷贝
  const cleanedNode = { ...node };

  // 删除指定字段
  delete cleanedNode.children;
  delete cleanedNode.components;
  delete cleanedNode.componentSets;

  return cleanedNode;
}

// 读取 figma.json 文件
const figmaData = JSON.parse(fs.readFileSync('figma.json', 'utf-8'));

// 测试查找节点 "500:4293"
const targetId = "500:4293";
let targetNode = null;

// 检查是否直接在 nodes 中
if (figmaData.nodes && figmaData.nodes[targetId]) {
  targetNode = figmaData.nodes[targetId].document;
} else {
  // 如果不在直接节点中，在所有节点的文档中递归查找
  if (figmaData.nodes) {
    for (const nodeKey of Object.keys(figmaData.nodes)) {
      const nodeInfo = figmaData.nodes[nodeKey];
      if (nodeInfo && nodeInfo.document) {
        targetNode = findNodeById(nodeInfo.document, targetId);
        if (targetNode) {
          break;
        }
      }
    }
  }
}

if (targetNode) {
  console.log('找到节点:', targetId);
  const cleanedNode = cleanNodeData(targetNode);
  console.log('清理后的节点信息:');
  console.log(JSON.stringify(cleanedNode, null, 2));
} else {
  console.log('未找到节点:', targetId);
}
