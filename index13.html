<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Today Page</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #000;
            color: #fff;
        }

        .today-page {
            position: relative;
            width: 3.61rem;
            background-color: #000;
            padding: 0.24rem;
        }

        .today-page-container {
            position: relative;
            width: 100%;
        }

        .container-1 {
            position: relative;
            width: 100%;
            margin-bottom: 0.24rem;
        }

        .page-title-default {
            position: relative;
            width: 100%;
            height: 0.24rem;
            background-color: #000;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 0;
            margin-bottom: 0.24rem;
        }

        .icon {
            width: 0.24rem;
            height: 0.24rem;
            background-color: #393939;
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
        }

        .menu-icon {
            background-color: #393939;
        }

        .back-icon {
            background-color: #393939;
        }

        .index-text {
            flex: 1;
            text-align: center;
            font-size: 0.16rem;
            font-weight: 500;
            color: #fff;
            line-height: 0.208rem;
        }

        .container-2 {
            position: relative;
            width: 100%;
        }

        .to-do-list-container {
            position: relative;
            width: 100%;
            margin-bottom: 0.16rem;
        }

        .to-do-list-frame {
            position: relative;
            width: 100%;
            background-color: #000;
            border: 1px solid #393939;
            border-radius: 0.08rem;
            padding: 0.08rem;
        }

        .title-container {
            position: relative;
            width: 100%;
            height: 0.24rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.16rem;
        }

        .to-do-list-title {
            font-size: 0.14rem;
            font-weight: 500;
            color: #fff;
            line-height: 0.182rem;
        }

        .plus-icon {
            width: 0.24rem;
            height: 0.24rem;
            background-color: #393939;
            border-radius: 1rem;
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
        }

        .mobile-app-container {
            position: relative;
            width: 100%;
            margin-bottom: 0.16rem;
        }

        .cards {
            position: relative;
            width: 100%;
            background-color: #000;
            border: 1px solid #393939;
            border-radius: 0.08rem;
            padding: 0.12rem 0.08rem;
        }

        .card-container-1 {
            position: relative;
            width: 100%;
            margin-bottom: 0.16rem;
        }

        .title-description-icon {
            position: relative;
            width: 100%;
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            margin-bottom: 0.08rem;
        }

        .title-description {
            flex: 1;
            margin-right: 0.08rem;
        }

        .mobile-app-design {
            font-size: 0.12rem;
            font-weight: 500;
            color: #fff;
            line-height: 0.156rem;
            margin-bottom: 0.04rem;
        }

        .wireframing-colors-fonts {
            font-size: 0.10rem;
            font-weight: 400;
            color: #aaa;
            line-height: 0.13rem;
        }

        .dot-icon {
            width: 0.18rem;
            height: 0.18rem;
            background-color: #393939;
            border-radius: 1rem;
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
        }

        .progress-bar-percentage {
            position: relative;
            width: 100%;
            margin-bottom: 0.16rem;
        }

        .progress-text {
            font-size: 0.10rem;
            font-weight: 400;
            color: #fff;
            line-height: 0.13rem;
            text-align: right;
            margin-bottom: 0.08rem;
        }

        .progress-bar {
            position: relative;
            width: 100%;
            height: 0.08rem;
            background-color: #717171;
            border-radius: 0.08rem;
        }

        .progress-fill {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            background-color: #c6c6c6;
            border-radius: 0.08rem;
        }

        .progress-30 {
            width: 30%;
        }

        .progress-50 {
            width: 50%;
        }

        .container-2-bottom {
            position: relative;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .date-container {
            background-color: #393939;
            border-radius: 0.12rem;
            padding: 0.10rem;
        }

        .date-text {
            font-size: 0.10rem;
            font-weight: 400;
            color: #aaa;
            line-height: 0.13rem;
        }

        .icon-container {
            display: flex;
            align-items: center;
            gap: 0.04rem;
        }

        .comment-container, .copy-link-container {
            display: flex;
            align-items: center;
            gap: 0.02rem;
        }

        .comment-icon, .copy-link-icon {
            width: 0.18rem;
            height: 0.18rem;
            background-color: #393939;
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
        }

        .comment-count, .copy-count {
            font-size: 0.10rem;
            font-weight: 500;
            color: #aaa;
            line-height: 0.13rem;
        }
    </style>
</head>
<body>
    <div class="today-page">
        <div class="today-page-container">
            <div class="container-1">
                <div class="page-title-default">
                    <div class="icon menu-icon"></div>
                    <div class="index-text">Index</div>
                    <div class="icon back-icon"></div>
                </div>
            </div>
            
            <div class="container-2">
                <div class="to-do-list-container">
                    <div class="to-do-list-frame">
                        <div class="title-container">
                            <div class="to-do-list-title">To do List</div>
                            <div class="plus-icon"></div>
                        </div>
                        
                        <div class="mobile-app-container">
                            <div class="cards">
                                <div class="card-container-1">
                                    <div class="title-description-icon">
                                        <div class="title-description">
                                            <div class="mobile-app-design">Mobile App Design</div>
                                            <div class="wireframing-colors-fonts">Wireframing, Colors, Fonts</div>
                                        </div>
                                        <div class="dot-icon"></div>
                                    </div>
                                    
                                    <div class="progress-bar-percentage">
                                        <div class="progress-text">Progress 30%</div>
                                        <div class="progress-bar">
                                            <div class="progress-fill progress-30"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="container-2-bottom">
                                        <div class="date-container">
                                            <div class="date-text">22 March 2025</div>
                                        </div>
                                        <div class="icon-container">
                                            <div class="comment-container">
                                                <div class="comment-icon"></div>
                                                <div class="comment-count">3</div>
                                            </div>
                                            <div class="copy-link-container">
                                                <div class="copy-link-icon"></div>
                                                <div class="copy-count">5</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="card-container-1">
                                    <div class="title-description-icon">
                                        <div class="title-description">
                                            <div class="mobile-app-design">Web App Design</div>
                                            <div class="wireframing-colors-fonts">Wireframing, Colors, Fonts</div>
                                        </div>
                                        <div class="dot-icon"></div>
                                    </div>
                                    
                                    <div class="progress-bar-percentage">
                                        <div class="progress-text">Progress 50%</div>
                                        <div class="progress-bar">
                                            <div class="progress-fill progress-50"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="container-2-bottom">
                                        <div class="date-container">
                                            <div class="date-text">22 March 2025</div>
                                        </div>
                                        <div class="icon-container">
                                            <div class="comment-container">
                                                <div class="comment-icon"></div>
                                                <div class="comment-count">3</div>
                                            </div>
                                            <div class="copy-link-container">
                                                <div class="copy-link-icon"></div>
                                                <div class="copy-count">5</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        (function () {
            const designWidth = 1125; // 设计稿宽度
            const baseRem = 100;      // 设定 1rem = 100px，方便换算

            function setRootFontSize() {
                const html = document.documentElement;
                const clientWidth = html.clientWidth;

                // 让页面宽度和设计稿成等比缩放
                html.style.fontSize = (clientWidth / designWidth) * baseRem + 'px';
            }

            setRootFontSize();
            window.addEventListener('resize', setRootFontSize);
        })();
    </script>
</body>
</html>
