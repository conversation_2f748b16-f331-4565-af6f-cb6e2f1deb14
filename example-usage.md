# 优化后的 getFigmaInfoFromUrl 函数使用示例

## 函数签名
```typescript
export async function getFigmaInfoFromUrl(figmaUrl: string, nodeId?: string): Promise<FigmaNodesResponse | any>
```

## 使用方式

### 1. 获取文件基本信息（原有功能）
```typescript
const figmaUrl = "https://www.figma.com/file/xxx/xxx";
const fileInfo = await getFigmaInfoFromUrl(figmaUrl);
console.log(fileInfo);
// 返回文件基本信息，包含 name, role, lastModified 等字段
```

### 2. 获取指定节点的简化信息（新功能）
```typescript
const figmaUrl = "https://www.figma.com/file/xxx/xxx";
const nodeId = "500:4293";
const nodeInfo = await getFigmaInfoFromUrl(figmaUrl, nodeId);
console.log(nodeInfo);
```

## 返回格式示例

当传入 `nodeId: "500:4293"` 时，返回的简化节点信息：

```json
{
  "id": "500:4293",
  "name": "Today Page",
  "type": "FRAME",
  "scrollBehavior": "SCROLLS",
  "boundVariables": {
    "paddingLeft": {
      "type": "VARIABLE_ALIAS",
      "id": "VariableID:2:48"
    },
    "paddingTop": {
      "type": "VARIABLE_ALIAS",
      "id": "VariableID:19:7"
    },
    "paddingRight": {
      "type": "VARIABLE_ALIAS",
      "id": "VariableID:2:48"
    },
    "paddingBottom": {
      "type": "VARIABLE_ALIAS",
      "id": "VariableID:19:7"
    },
    "fills": [
      {
        "type": "VARIABLE_ALIAS",
        "id": "VariableID:2:8"
      }
    ]
  },
  "explicitVariableModes": {
    "VariableCollectionId:1:35": "2:2"
  },
  "blendMode": "PASS_THROUGH",
  "clipsContent": true,
  "background": [...],
  "fills": [...],
  "strokes": [],
  "strokeWeight": 1,
  "strokeAlign": "INSIDE",
  "backgroundColor": {...},
  "layoutGrids": [...],
  "layoutMode": "VERTICAL",
  "counterAxisSizingMode": "FIXED",
  "primaryAxisSizingMode": "FIXED",
  "paddingLeft": 16,
  "paddingRight": 16,
  "paddingTop": 32,
  "paddingBottom": 32,
  "layoutWrap": "NO_WRAP",
  "absoluteBoundingBox": {...},
  "absoluteRenderBounds": {...},
  "constraints": {...},
  "layoutSizingHorizontal": "FIXED",
  "layoutSizingVertical": "FIXED",
  "exportSettings": [...],
  "effects": [],
  "interactions": []
}
```

## 已删除的字段

为了简化返回数据，以下字段已被删除：
- `children` - 子节点数组
- `components` - 组件信息
- `componentSets` - 组件集信息

## 查找逻辑

函数会按以下顺序查找指定的节点：
1. 首先检查是否直接在 `fileData.nodes[nodeId]` 中
2. 如果不在，则在所有节点的文档中递归查找
3. 如果还没找到，尝试在 `document.children` 中查找（兼容其他数据格式）
4. 如果最终没找到，抛出错误

这种多层查找机制确保了函数能够处理不同格式的 Figma 数据结构。
